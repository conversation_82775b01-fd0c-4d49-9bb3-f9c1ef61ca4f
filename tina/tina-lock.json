{"schema": {"version": {"fullVersion": "1.6.0", "major": "1", "minor": "6", "patch": "0"}, "meta": {"flags": ["experimentalData"]}, "collections": [{"name": "post", "label": "Blog Post", "path": "src/content/blog", "format": "mdx", "fields": [{"type": "image", "label": "Cover Image", "required": true, "name": "heroImage", "description": "The image used for the cover of the post", "namespace": ["post", "heroImage"], "searchable": false, "uid": false}, {"type": "string", "label": "Meta Title", "required": false, "name": "metaTitle", "description": "SEO meta title for the post (optional)", "namespace": ["post", "metaTitle"], "searchable": true, "uid": false}, {"type": "string", "label": "Slug", "required": false, "name": "slug", "description": "Custom URL slug for the post (optional)", "namespace": ["post", "slug"], "searchable": true, "uid": false}, {"type": "string", "required": true, "name": "category", "label": "Category", "description": "Select an category for this post", "options": ["News", "Guides", "Company", "Engineering"], "namespace": ["post", "category"], "searchable": true, "uid": false}, {"type": "string", "label": "description", "required": true, "name": "description", "description": "A short description of the post", "namespace": ["post", "description"], "searchable": true, "uid": false}, {"type": "datetime", "name": "pubDate", "label": "Publication Date", "required": true, "namespace": ["post", "pubDate"], "searchable": true, "uid": false}, {"name": "draft", "label": "Draft", "type": "boolean", "description": "If this is checked the post will not be published", "namespace": ["post", "draft"], "searchable": true, "uid": false}, {"type": "string", "name": "tags", "required": true, "label": "Tags", "description": "Tags for this post", "list": true, "ui": {"component": "tags"}, "namespace": ["post", "tags"], "searchable": true, "uid": false}, {"type": "string", "name": "title", "label": "Title", "isTitle": true, "required": true, "namespace": ["post", "title"], "searchable": true, "uid": false}, {"type": "rich-text", "label": "Body", "name": "SButton", "isBody": true, "templates": [{"label": "SButton", "name": "SButton", "fields": [{"type": "rich-text", "label": "SButton", "name": "children", "isBody": true, "namespace": ["post", "SButton", "SButton", "children"], "searchable": true, "parser": {"type": "mdx"}, "uid": false}], "namespace": ["post", "SButton", "SButton"]}], "namespace": ["post", "SButton"], "searchable": true, "parser": {"type": "mdx"}, "uid": false}], "namespace": ["post"]}], "config": {"media": {"tina": {"publicFolder": "", "mediaRoot": "/public/assets/images"}}}}, "lookup": {"DocumentConnection": {"type": "DocumentConnection", "resolveType": "multiCollectionDocumentList", "collections": ["post"]}, "Node": {"type": "Node", "resolveType": "nodeDocument"}, "DocumentNode": {"type": "DocumentNode", "resolveType": "multiCollectionDocument", "createDocument": "create", "updateDocument": "update"}, "Post": {"type": "Post", "resolveType": "collectionDocument", "collection": "post", "createPost": "create", "updatePost": "update"}, "PostConnection": {"type": "PostConnection", "resolveType": "collectionDocumentList", "collection": "post"}}, "graphql": {"kind": "Document", "definitions": [{"kind": "ScalarTypeDefinition", "name": {"kind": "Name", "value": "Reference"}, "description": {"kind": "StringValue", "value": "References another document, used as a foreign key"}, "directives": []}, {"kind": "ScalarTypeDefinition", "name": {"kind": "Name", "value": "JSON"}, "description": {"kind": "StringValue", "value": ""}, "directives": []}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SystemInfo"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "filename"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "basename"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "hasReferences"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "breadcrumbs"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "excludeExtension"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}], "type": {"kind": "NonNullType", "type": {"kind": "ListType", "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "path"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "relativePath"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "extension"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "template"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "collection"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Collection"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "Folder"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "name"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "path"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageInfo"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "hasPreviousPage"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "hasNextPage"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "startCursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "endCursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "InterfaceTypeDefinition", "description": {"kind": "StringValue", "value": ""}, "name": {"kind": "Name", "value": "Node"}, "interfaces": [], "directives": [], "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}]}, {"kind": "InterfaceTypeDefinition", "description": {"kind": "StringValue", "value": ""}, "name": {"kind": "Name", "value": "Document"}, "interfaces": [], "directives": [], "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_sys"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SystemInfo"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_values"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}]}, {"kind": "InterfaceTypeDefinition", "description": {"kind": "StringValue", "value": "A relay-compliant pagination connection"}, "name": {"kind": "Name", "value": "Connection"}, "interfaces": [], "directives": [], "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "totalCount"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageInfo"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageInfo"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "Query"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "getOptimizedQuery"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "queryString"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "collection"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Collection"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "collections"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "ListType", "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Collection"}}}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "node"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "id"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Node"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "document"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "post"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Post"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "postConnection"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "before"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "after"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "first"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "last"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "sort"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "filter"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON>er"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PostConnection"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "DocumentFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "post"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON>er"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "DocumentConnectionEdges"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "cursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "node"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Connection"}}], "directives": [], "name": {"kind": "Name", "value": "DocumentConnection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageInfo"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "totalCount"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "edges"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentConnectionEdges"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "Collection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "name"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "slug"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "label"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "path"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "format"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "matches"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "templates"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "fields"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "documents"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "before"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "after"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "first"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "last"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "sort"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "filter"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "folder"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentConnection"}}}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "DocumentNode"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Post"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Folder"}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Node"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Document"}}], "directives": [], "name": {"kind": "Name", "value": "Post"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "heroImage"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "metaTitle"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "slug"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "category"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "description"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pubDate"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "draft"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "tags"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "ListType", "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "SButton"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_sys"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SystemInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_values"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "ImageFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "startsWith"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "eq"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "exists"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "in"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "StringFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "startsWith"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "eq"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "exists"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "in"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "DatetimeFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "after"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "before"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "eq"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "exists"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "in"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "eq"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "exists"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "RichTextFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "startsWith"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "eq"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "exists"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PostSButtonSButtonFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "children"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RichTextFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PostSButtonFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "SButton"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PostSButtonSButtonFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON>er"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "heroImage"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ImageFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "metaTitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "slug"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "category"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "pubDate"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DatetimeFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "draft"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "tags"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "SButton"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PostSButtonFilter"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PostConnectionEdges"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "cursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "node"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Post"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Connection"}}], "directives": [], "name": {"kind": "Name", "value": "PostConnection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageInfo"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "totalCount"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "edges"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PostConnectionEdges"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "Mutation"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "addPendingDocument"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "template"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "updateDocument"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentUpdateMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "deleteDocument"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "createDocument"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "createFolder"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "updatePost"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PostMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Post"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "createPost"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PostMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Post"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "DocumentUpdateMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "post"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PostMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "DocumentMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "post"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PostMutation"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PostMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "heroImage"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "metaTitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "slug"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "category"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "pubDate"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "draft"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "tags"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "SButton"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}]}]}}